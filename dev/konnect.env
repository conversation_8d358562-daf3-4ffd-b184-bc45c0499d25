KONG_ROLE=data_plane
KONG_DATABASE=off
KONG_VITALS=off
KONG_CLUSTER_MTLS=pki
KONG_CLUSTER_CONTROL_PLANE="0b573f5c48.us.cp0.konghq.com:443"
KONG_CLUSTER_SERVER_NAME=0b573f5c48.us.cp0.konghq.com
KONG_CLUSTER_TELEMETRY_ENDPOINT="0b573f5c48.us.tp0.konghq.com:443"
KONG_CLUSTER_TELEMETRY_SERVER_NAME=0b573f5c48.us.tp0.konghq.com
KONG_CLUSTER_CERT="-----BEGIN CERTIFICATE-----
MIICHjCCAcSgAwIBAgIBATAKBggqhkjOPQQDBDBAMT4wCQYDVQQGEwJVUzAxBgNV
BAMeKgBrAG8AbgBuAGUAYwB0AC0AZQB2AGUAbgB0AC0AZwBhAHQAZQB3AGEAeTAe
Fw0yNTA2MjUyMDE4MTBaFw0zNTA2MjUyMDE4MTBaMEAxPjAJBgNVBAYTAlVTMDEG
A1UEAx4qAGsAbwBuAG4AZQBjAHQALQBlAHYAZQBuAHQALQBnAGEAdABlAHcAYQB5
MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEABwRYHYUqoKoB3LaPJwK9xZhzY2Q
CUvEEJ54WsFWFJDb9mcn0+4MjRq6InmwaQ7jzXcW+9xcrIRdvAGf1oasT6OBrjCB
qzAMBgNVHRMBAf8EAjAAMAsGA1UdDwQEAwIABjAdBgNVHSUEFjAUBggrBgEFBQcD
AQYIKwYBBQUHAwIwFwYJKwYBBAGCNxQCBAoMCGNlcnRUeXBlMCMGCSsGAQQBgjcV
AgQWBBQBAQEBAQEBAQEBAQEBAQEBAQEBATAcBgkrBgEEAYI3FQcEDzANBgUpAQEB
AQIBCgIBFDATBgkrBgEEAYI3FQEEBgIEABQACjAKBggqhkjOPQQDBANIADBFAiBH
59BU27Ftiqsn3VexFOR6YxOQtBCI0RFW9633hBUJNgIhAPrkje40AyCyIHxJ2QZQ
GpiEl1HnOK4B/e+J6jZbgPdF
-----END CERTIFICATE-----"
KONG_CLUSTER_CERT_KEY="*****************************************************************************************************************************************************************************************************************************************************************"
KONG_LUA_SSL_TRUSTED_CERTIFICATE=system
KONG_KONNECT_MODE=on
KONG_CLUSTER_DP_LABELS=type:docker-macOsArmOS
KONG_ROUTER_FLAVOR=expressions