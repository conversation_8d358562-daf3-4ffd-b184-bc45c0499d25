_format_version: "3.0"
_transform: true

routes:
- name: solace-producer
  paths:
  - /solace/producer
- name: kafka-producer
  paths:
  - /kafka/producer

plugins:
# - name: solace-upstream
#   route: solace-producer
- name: kafka-upstream
  route: kafka-producer
  config:
    topic: my-topic-incoming
    producer_async: false
    schema_registry:
      confluent:
        url: http://schema-registry:8080/apis/ccompat/v7
        authentication:
          mode: none
          # basic:
          #   username: user
          #   password: password
        value_schema:
          subject_name: user-json-value
          schema_version: latest    
    bootstrap_servers:
    - host: kafka
      port: 9092
